import React from 'react';
import { AlertTriangle, Shield, CheckCircle } from 'lucide-react';

export const MultithreadingProblemsSection = () => {
  return (
    <div className="mb-12">
      <div className="flex items-center gap-3 mb-6">
        <div className="p-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl">
          <AlertTriangle className="w-8 h-8 text-white" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900">III. <PERSON> và trò chơi nguy hiểm - Vấn đề đa luồng</h2>
      </div>
      
      <p className="text-gray-700 leading-relaxed text-lg mb-6">
        Trong thế giới đa luồng, Loki đại diện cho những vấn đề phức tạp và nguy hiểm mà các lập trình viên phải đối mặt. Giống như <PERSON> luôn tạo ra những tình huống rắc rối và kh<PERSON>, các vấn đề đa luồng nh<PERSON>, Data Race, và Race Condition có thể khiến ứng dụng trở nên không ổn định và khó debug.
      </p>

      <h4 className="text-xl font-semibold text-gray-900 mb-4">1. Deadlock - Bế tắc chết người</h4>
      <div className="bg-gradient-to-r from-red-50 to-pink-50 p-6 rounded-xl mb-6">
        <p className="text-gray-700 leading-relaxed mb-4">
          Loki cười nham hiểm: &ldquo;Hãy tưởng tượng Tony và Thor đang cần hai tài nguyên để hoàn thành nhiệm vụ của mình. Tony cần búa của Thor và chiếc áo giáp của chính mình. Trong khi đó, Thor cần chiếc áo giáp của Tony và búa của chính mình.&rdquo;
        </p>
        
        <div className="bg-white p-4 rounded-lg mb-4">
          <h5 className="font-semibold text-red-700 mb-3">Tình huống Deadlock:</h5>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="bg-red-50 p-3 rounded-lg">
              <h6 className="font-semibold text-red-600 mb-2">Tony Stark (Thread 1)</h6>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>1. Lấy áo giáp của mình (Lock A)</li>
                <li>2. Chờ búa của Thor (Lock B)</li>
                <li>3. <span className="text-red-600 font-semibold">Bị chặn vô thời hạn</span></li>
              </ul>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg">
              <h6 className="font-semibold text-blue-600 mb-2">Thor (Thread 2)</h6>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>1. Lấy búa của mình (Lock B)</li>
                <li>2. Chờ áo giáp của Tony (Lock A)</li>
                <li>3. <span className="text-red-600 font-semibold">Bị chặn vô thời hạn</span></li>
              </ul>
            </div>
          </div>
        </div>

        <p className="text-gray-700 leading-relaxed mb-4">
          Loki: &ldquo;Cả hai đều đang chờ đợi tài nguyên mà người kia đang nắm giữ. Không ai chịu nhường bước, và kết quả là cả hai đều bị kẹt mãi mãi. Đây chính là deadlock!&rdquo;
        </p>

        <div className="bg-yellow-50 p-4 rounded-lg">
          <h6 className="font-semibold text-yellow-700 mb-2">4 điều kiện để xảy ra Deadlock (Coffman Conditions):</h6>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• <strong>Mutual Exclusion:</strong> Tài nguyên chỉ có thể được sử dụng bởi một thread tại một thời điểm</li>
            <li>• <strong>Hold and Wait:</strong> Thread giữ ít nhất một tài nguyên và chờ đợi tài nguyên khác</li>
            <li>• <strong>No Preemption:</strong> Tài nguyên không thể bị tước đoạt từ thread đang sử dụng</li>
            <li>• <strong>Circular Wait:</strong> Tồn tại một chuỗi thread chờ đợi lẫn nhau theo vòng tròn</li>
          </ul>
        </div>
      </div>

      <h4 className="text-xl font-semibold text-gray-900 mb-4">2. Data Race - Cuộc đua dữ liệu</h4>
      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 p-6 rounded-xl mb-6">
        <p className="text-gray-700 leading-relaxed mb-4">
          Loki tiếp tục: &ldquo;Bây giờ, hãy tưởng tượng có một chiếc két chứa tiền của Avengers. Cả Tony và Thor đều muốn rút tiền cùng một lúc mà không có bất kỳ sự phối hợp nào.&rdquo;
        </p>
        
        <div className="bg-white p-4 rounded-lg mb-4">
          <h5 className="font-semibold text-orange-700 mb-3">Tình huống Data Race:</h5>
          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700"><strong>Bước 1:</strong> Số dư ban đầu: $1000</p>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-red-50 p-3 rounded-lg">
                <h6 className="font-semibold text-red-600 mb-2">Tony (Thread 1)</h6>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>1. Đọc số dư: $1000</li>
                  <li>2. Tính toán: $1000 - $300 = $700</li>
                  <li>3. Ghi lại: $700</li>
                </ul>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <h6 className="font-semibold text-blue-600 mb-2">Thor (Thread 2)</h6>
                <ul className="text-xs text-gray-600 space-y-1">
                  <li>1. Đọc số dư: $1000 (cùng lúc với Tony)</li>
                  <li>2. Tính toán: $1000 - $200 = $800</li>
                  <li>3. Ghi lại: $800</li>
                </ul>
              </div>
            </div>
            <div className="bg-red-100 p-3 rounded-lg">
              <p className="text-sm text-red-700"><strong>Kết quả sai:</strong> Số dư cuối cùng có thể là $700 hoặc $800 thay vì $500 như mong đợi!</p>
            </div>
          </div>
        </div>

        <p className="text-gray-700 leading-relaxed">
          Loki: &ldquo;Vấn đề ở đây là cả hai thread đều đọc cùng một giá trị ban đầu và thực hiện tính toán độc lập. Kết quả cuối cùng phụ thuộc vào thread nào ghi sau cùng, dẫn đến dữ liệu không nhất quán.&rdquo;
        </p>
      </div>

      <h4 className="text-xl font-semibold text-gray-900 mb-4">3. Race Condition - Điều kiện đua</h4>
      <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-6 rounded-xl mb-6">
        <p className="text-gray-700 leading-relaxed mb-4">
          Loki: &ldquo;Race Condition xảy ra khi kết quả của chương trình phụ thuộc vào thứ tự thực thi của các thread. Giống như một cuộc đua, ai chạy nhanh hơn sẽ quyết định kết quả cuối cùng.&rdquo;
        </p>
        
        <div className="bg-white p-4 rounded-lg mb-4">
          <h5 className="font-semibold text-purple-700 mb-3">Ví dụ Race Condition:</h5>
          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-700"><strong>Biến toàn cục:</strong> counter = 0</p>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="bg-green-50 p-3 rounded-lg">
                <h6 className="font-semibold text-green-600 mb-2">Thread A</h6>
                <pre className="text-xs text-gray-600">
{`for (int i = 0; i < 1000; i++) {
    counter++;  // counter = counter + 1
}`}
                </pre>
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <h6 className="font-semibold text-blue-600 mb-2">Thread B</h6>
                <pre className="text-xs text-gray-600">
{`for (int i = 0; i < 1000; i++) {
    counter++;  // counter = counter + 1
}`}
                </pre>
              </div>
            </div>
            <div className="bg-yellow-100 p-3 rounded-lg">
              <p className="text-sm text-yellow-700">
                <strong>Kết quả mong đợi:</strong> counter = 2000<br/>
                <strong>Kết quả thực tế:</strong> counter có thể là bất kỳ giá trị nào từ 2 đến 2000!
              </p>
            </div>
          </div>
        </div>

        <p className="text-gray-700 leading-relaxed">
          Loki: &ldquo;Vấn đề ở đây là phép toán counter++ không phải là atomic. Nó bao gồm ba bước: đọc giá trị, tăng lên 1, và ghi lại. Nếu hai thread thực hiện đồng thời, chúng có thể can thiệp vào nhau.&rdquo;
        </p>
      </div>

      <h4 className="text-xl font-semibold text-gray-900 mb-4">4. Giải pháp - Các siêu anh hùng đánh bại Loki</h4>
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-6 rounded-xl mb-6">
        <div className="flex items-center gap-3 mb-4">
          <Shield className="w-6 h-6 text-green-600" />
          <h5 className="font-semibold text-green-700">Synchronization - Đồng bộ hóa</h5>
        </div>

        <p className="text-gray-700 leading-relaxed mb-4">
          Captain America: &ldquo;Để đánh bại Loki, chúng ta cần phối hợp và đồng bộ hóa các hành động. Trong lập trình đa luồng, chúng ta có nhiều công cụ để đảm bảo các thread hoạt động một cách an toàn.&rdquo;
        </p>

        <div className="grid md:grid-cols-2 gap-4 mb-4">
          <div className="bg-white p-4 rounded-lg border border-green-200">
            <h6 className="font-semibold text-green-600 mb-3">Synchronized Keyword (Java)</h6>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded mb-2">
{`public synchronized void withdraw(int amount) {
    if (balance >= amount) {
        balance -= amount;
    }
}`}
            </pre>
            <p className="text-xs text-gray-500">Đảm bảo chỉ một thread có thể truy cập method tại một thời điểm</p>
          </div>

          <div className="bg-white p-4 rounded-lg border border-blue-200">
            <h6 className="font-semibold text-blue-600 mb-3">ReentrantLock (Java)</h6>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded mb-2">
{`private final ReentrantLock lock = new ReentrantLock();

public void withdraw(int amount) {
    lock.lock();
    try {
        if (balance >= amount) {
            balance -= amount;
        }
    } finally {
        lock.unlock();
    }
}`}
            </pre>
            <p className="text-xs text-gray-500">Cung cấp khả năng kiểm soát lock linh hoạt hơn</p>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg border border-purple-200">
            <h6 className="font-semibold text-purple-600 mb-3">Atomic Variables (Java)</h6>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded mb-2">
{`private AtomicInteger counter = new AtomicInteger(0);

public void increment() {
    counter.incrementAndGet();  // Thread-safe
}`}
            </pre>
            <p className="text-xs text-gray-500">Thực hiện các phép toán atomic mà không cần lock</p>
          </div>

          <div className="bg-white p-4 rounded-lg border border-orange-200">
            <h6 className="font-semibold text-orange-600 mb-3">Semaphore</h6>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded mb-2">
{`private Semaphore semaphore = new Semaphore(3);

public void accessResource() {
    semaphore.acquire();
    try {
        // Truy cập tài nguyên
    } finally {
        semaphore.release();
    }
}`}
            </pre>
            <p className="text-xs text-gray-500">Kiểm soát số lượng thread truy cập tài nguyên</p>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-cyan-50 p-6 rounded-xl mb-6">
        <div className="flex items-center gap-3 mb-4">
          <CheckCircle className="w-6 h-6 text-blue-600" />
          <h5 className="font-semibold text-blue-700">Chiến lược tránh Deadlock</h5>
        </div>

        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-white p-4 rounded-lg">
            <h6 className="font-semibold text-green-600 mb-2">Lock Ordering</h6>
            <p className="text-sm text-gray-600 mb-2">Luôn luôn acquire locks theo một thứ tự cố định</p>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
{`// Luôn lock theo thứ tự: lockA trước, lockB sau
synchronized(lockA) {
    synchronized(lockB) {
        // Critical section
    }
}`}
            </pre>
          </div>

          <div className="bg-white p-4 rounded-lg">
            <h6 className="font-semibold text-blue-600 mb-2">Timeout</h6>
            <p className="text-sm text-gray-600 mb-2">Sử dụng timeout để tránh chờ đợi vô thời hạn</p>
            <pre className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
{`if (lock.tryLock(5, TimeUnit.SECONDS)) {
    try {
        // Critical section
    } finally {
        lock.unlock();
    }
} else {
    // Handle timeout
}`}
            </pre>
          </div>
        </div>
      </div>

      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 p-6 rounded-xl">
        <h5 className="font-semibold text-indigo-700 mb-3">Best Practices - Lời khuyên từ các siêu anh hùng</h5>
        <div className="grid md:grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg">
            <h6 className="font-semibold text-red-600 mb-2">Tony Stark</h6>
            <p className="text-sm text-gray-600">&ldquo;Minimize shared state - Giảm thiểu việc chia sẻ dữ liệu giữa các thread&rdquo;</p>
          </div>

          <div className="bg-white p-4 rounded-lg">
            <h6 className="font-semibold text-blue-600 mb-2">Captain America</h6>
            <p className="text-sm text-gray-600">&ldquo;Use immutable objects - Sử dụng các đối tượng không thể thay đổi khi có thể&rdquo;</p>
          </div>

          <div className="bg-white p-4 rounded-lg">
            <h6 className="font-semibold text-green-600 mb-2">Hulk</h6>
            <p className="text-sm text-gray-600">&ldquo;Keep critical sections small - Giữ các đoạn code cần đồng bộ hóa ngắn gọn&rdquo;</p>
          </div>
        </div>
      </div>
    </div>
  );
};
